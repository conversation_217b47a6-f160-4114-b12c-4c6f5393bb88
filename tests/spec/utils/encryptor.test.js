import { encrypt, decrypt } from '../../../src/utils/encryptor';

describe('Success Scenario', () => {
  test('encrypt', () => {
    const encrypted = encrypt('test');
    expect(encrypted).not.toBe('test');
  });

  test('decrypt', () => {
    const encrypted = encrypt('test');
    expect(decrypt(encrypted)).toBe('test');
  });
});

describe('Fails when non-string value provided', () => {
  const getErrorMessage = (data) => `The "data" argument must be of type string or an instance of Buffer, TypedArray, or DataView. Received type ${typeof data} (${data})`;
  const getErrorMessageForDecrypt = (data) => `The first argument must be of type string or an instance of <PERSON>uffer, ArrayBuffer, or Array or an Array-like Object. Received type ${typeof data} (${data})`;
  test('encrypt: Integer', () => {
    const testValue = 12345;
    const errorMessage = getErrorMessage(testValue);
    const t = () => {
      encrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });

  test('encrypt: Boolean', () => {
    const testValue = false;
    const errorMessage = getErrorMessage(testValue);
    const t = () => {
      encrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });

  test('encrypt: Object', () => {
    const testValue = [{ test: 'test' }];
    const errorMessage = 'The "data" argument must be of type string or an instance of Buffer, TypedArray, or DataView. Received an instance of Array';
    const t = () => {
      encrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });

  test('decrypt: Integer', () => {
    const testValue = 12345;
    const errorMessage = getErrorMessageForDecrypt(testValue);
    const t = () => {
      decrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });

  test('decrypt: Boolean', () => {
    const testValue = false;
    const errorMessage = getErrorMessageForDecrypt(testValue);
    const t = () => {
      decrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });

  test('decrypt: Object', () => {
    const testValue = [{ test: 'test' }];
    const errorMessage = 'Invalid initialization vector';
    const t = () => {
      decrypt(testValue);
    };
    expect(t).toThrow(errorMessage);
  });
});

describe('Fails when unencrypted text provided to decrypt', () => {
  const sourceText = 'HelloWorld';
  const encryped = encrypt(sourceText);
  const randomEncrptedText = 'KxvkeO7x0BopEwXP1FsrE670arIHU0GiLFYQPmvFjkbN8qxmTqDo8IJu7oQDSI3ICsRdn5xVp+5l7U0Vk+KpQkVHICjho3N3SM4jPpmsHjXb23M7k4ppOlSllWiPHqC8oUmRoDv2XFgHBZ==';
  test('decrypting unencrypted text', () => {
    const t = () => {
      decrypt(sourceText);
    };
    expect(t).toThrow('Invalid initialization vector');
  });

  test('decrypting unknown encrypted text (not generated by the encryptor)', () => {
    const t = () => {
      decrypt(randomEncrptedText);
    };
    expect(t).toThrow('Unsupported state or unable to authenticate data');
  });

  test('decrypting known encrypted text', () => {
    const t = () => {
      decrypt(encryped);
    };
    expect(t).not.toThrow();
    expect(decrypt(encryped)).toBe(sourceText);
  });
});
