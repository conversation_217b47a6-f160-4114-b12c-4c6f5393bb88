FROM node:22.13.0-alpine
RUN npm install pm2 -g
# Working Dir
RUN mkdir -p /unstoppable-leadership-api
WORKDIR /unstoppable-leadership-api
# Copy Package Json Files
COPY package*.json /unstoppable-leadership-api/
# Copy .env File
# COPY .env /unstoppable-leadership-api/
# Install Files
RUN npm i --legacy-peer-deps
# Copy Source Files
COPY . /unstoppable-leadership-api/
# Build
RUN npm run build
CMD [ "npm", "run", "serve:prod" ]