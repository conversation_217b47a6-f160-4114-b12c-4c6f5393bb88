{"name": "unstoppable-leadership-api", "version": "1.0.0", "description": "Unstoppable Leadership Api", "main": "app.js", "scripts": {"start": "nodemon --exec babel-node src/app.js", "clean": "rm -Rf ./dist", "build": "npm run clean; babel ./src --out-dir ./dist --copy-files", "serve": "node ./dist/app.js", "serve:prod": "pm2-runtime ./dist/app.js -- --migrate migrate", "db:migrate": "db-migrate up", "db:migrate-down": "db-migrate down", "db:clean": "db-migrate reset", "db:refresh": "npm run db:clean && npm run db:migrate", "create:migration": "db-migrate create", "lint": "eslint --fix --clear --color --ext .js,.jsx; exit 0", "test": "NODE_ENV=test jest --watchAll --colors --verbose --detectOpenHandles --coverage"}, "dependencies": {"@jest/globals": "^29.7.0", "@sendgrid/mail": "^8.1.0", "bcrypt": "^5.0.1", "bluebird": "^3.7.2", "body-parser": "^1.20.0", "convict": "^6.2.3", "cors": "^2.8.5", "db-migrate-pg": "^1.2.2", "dotenv": "^16.0.1", "express": "^4.18.1", "handlebars": "^4.7.8", "helmet": "^8.0.0", "joi": "^17.6.0", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment": "^2.29.3", "moment-timezone": "^0.5.44", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cmd": "^5.0.0", "node-fetch": "^3.3.2", "pg": "^8.7.3", "typedi": "^0.10.0", "uuid": "^11.0.5", "yargs": "^17.5.1"}, "devDependencies": {"@babel/cli": "^7.17.10", "@babel/core": "^7.18.5", "@babel/eslint-parser": "^7.18.2", "@babel/node": "^7.18.5", "@babel/preset-env": "^7.18.2", "@testcontainers/postgresql": "^10.8.1", "@types/jest": "^29.5.11", "babel-jest": "^29.7.0", "db-migrate": "^0.11.14", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^50.6.1", "eslint-watch": "^8.0.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "testcontainers": "^10.8.1"}}