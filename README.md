# unstoppable-leadership-api

## Dependencies:
- Node 20>= (javascript runtime)
- PostgreSQL (database)
- Docker (for unit testing, containerization)
- Sonar scanner (Optional)

## Development

1. Create a `.env` file using `.env.sample` and fill your database details.

2. Start the app

    `npm start -- --migrate clean_and_migrate`
    
If everything goes well, you should see the app at `localhost:8000`.

## JWT Key Pair

- For authentication of secured api routes we have used JWT tokens.
- For creation and verification of JWT token we have user ES512 algorithm.
  
  Commands to create JWT token key pair.

  `openssl ecparam -genkey -name secp521r1 -noout -out jwt-private.pem`

  `openssl ec -in jwt-private.pem -pubout > jwt-public.pem`

- After getting the public and private keys, update them in the `.env` file 
- While updating use double quotes to escape the newlines, it should look like

    ```sh
    JWT_PRIVATE_KEY="-----BEGIN EC PRIVATE KEY-----
    Some key string here"
    ```

## NPM Scripts

1. `npm run create:migration YOUR_MIGRATION_NAME`
   
   This command will create your migration in migrations directory along with up and down migration SQLs in migrations/sqls directory.

2. `npm run db:migrate`
   
    This command will run your up migration in migrations directory.

3. `npm run db:migrate-down`
   
    This command will run your down migration in migrations directory. All of the down migrations work identically to the up migrations by substituting the word down for up.

4. `npm run db:clean`

    This command will clean your database. It uses down migration to clean up the database.


5. `npm start`
   
    Starts the development server

6. `npm start -- --migrate migrate`

    This command will start your application and run migrations before starting server.

7. `npm start -- --migrate clean_and_migrate`

    This command will start your application and clean db plus run migrations before starting server.

8. `npm run lint`

    This command would show you linting errors in console log.


## Unit testing
We use jest to do the unit testing along with a library called [testcontainers](https://testcontainers.com/getting-started/) which can be used instead avoid mocking database connection.

1. This library requires docker to be setup properly. this library will create disposable database containers for us to use new database for every test suite
2. Create a directory called `tests` if did not exists
3. Create test files to cover up a file. for example if there is a file called `commonFunctions.js` create a test for it. It should be in `fileName.test.js`
4. use a `describe` block for a function.
5. Running `npm run test` will run all the unit test with coverage which will be later used in static code analysis tools like SonarQube.
6. If the npm test command is not setup properly you can use this command to `NODE_ENV=test npx jest --watchAll --colors --verbose --detectOpenHandles --coverage`

## Code Quality analysis
We use SonarQube to do the code quality analysis. you can setup this by updating the details in `sonar-project.properties` create if it does not exists
with the following contents.

- Generate a SonarQube user token
- Set it as env variable called `SONAR_TOKEN` or you can pass it as argument
- Download the sonarqube cli from [here](https://docs.sonarsource.com/sonarqube/latest/analyzing-source-code/scanners/sonarscanner/).
- Extract the contents, set it in the path
- run it by executing `sonar-scanner` with `sonar-scanner -Dsonar.token=your_token_here`

```properties
# required metadata

sonar.projectKey=project-name
sonar.projectName=Project name
sonar.host.url=the host url
sonar.projectVersion=1.0.0
sonar.sourceEncoding=UTF-8
sonar.eslint.eslintconfigpath=.eslintrc.js

# path to source directories
sonar.sources=src

# coverage report
sonar.javascript.lcov.reportPaths=coverage/lcov.info
```

If you are interested in setting up SonarQube in your local you can use the script `install-sonarqube.sh` in root or you can get the script from [here](https://gist.github.com/magesh-memorres/450ed9ea576fecdfb7159b0f926a636a) which installs sonarqube server running in port `9000`. 

## Docker

You can refer [this](https://docs.docker.com/engine/install/linux-postinstall/) step to run docker without adding `sudo` by either adding the current
user to the `docker` user group or using rootless mode

If you wish to use docker to start this app, run the following command:

1. `sudo docker-compose up` or `docker-compose up`

    (assuming docker and docker-compose is pre-installed on the system)

    This command will initialize the containers and run the containers using `docker-compose.yml` file.

2. If everything goes well, you should see the app at `localhost:8080`.

3. To clean up all the running docker container, run the following command:
    
    `sudo docker-compose down` or `docker-compose down`