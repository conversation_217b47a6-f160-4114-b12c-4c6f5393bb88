import { Container } from 'typedi';
import {
  routes, featureLevel, publicPost, publicPut,
} from '../../routes/utils';
import { SecurityService } from '../../externalServices';
import {
  loginSchema,
} from './schemas';
/**
  * Login/Signup end point
* */
export default () => {
  publicPost(
    featureLevel.production,
    routes.security.LOGIN,
    async (req) => {
      const service = Container.get(SecurityService);
      const { email, password } = await loginSchema.validateAsync(req.body);
      const { 'user-agent': userAgent } = req.headers;
      const requestMetadata = {
        userAgent,
        ip: req.ip,
      };
      return await service.login(requestMetadata, email, password);
    },
  );
};
