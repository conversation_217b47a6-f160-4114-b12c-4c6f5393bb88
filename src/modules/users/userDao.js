import moment from 'moment';
import { Client } from 'pg';
import { PasswordHash } from '../../models';
import { Role } from '../../auth';
import {
  QueryBuilder, Mapper, Queries,
  parserId, parserDate, parserInteger,
  STATUS,
} from '../../utils';
import { userDetailsUpdateMap, userPasswordUpdateMap, userUpdateMap } from './updateMaps';

class UserDao {
  userJoins = `LEFT JOIN user_roles ur ON ur.user_id = u.id
                  LEFT JOIN roles r ON r.id = ur.role_id
                  LEFT JOIN user_details ud ON ud.user_id = u.id
                  LEFT JOIN users uld ON uld.id = u.id\n`;

  userQuery = `SELECT u.id,u.status,u.email,u.password,u.status,u.created_on,ur.role_id,r.name as role, ud.first_name,
                ud.last_name, ud.profile_picture_path, uld.wrong_login_count, uld.last_wrong_login_attempt 
                FROM users u\n${this.userJoins}`;

  static MAX_TOKEN_VALIDITY = 3600;

  async createUser(client, createUserDto, createdBy) {
    const res = await client.query(
      `INSERT INTO users 
      (email, password, status, created_by, updated_by) 
      VALUES ($1, $2, $3, $4, $5) RETURNING id`,
      [createUserDto.email, createUserDto.password,
        createUserDto.status, createdBy, createdBy],
    );
    const userId = Mapper.getId(res);

    const detailsCreatedBy = createdBy || userId;

    await client.query(
      `INSERT INTO user_details
      (user_id, first_name, last_name, created_by, updated_by)
      VALUES ($1, $2, $3, $4, $5)`,
      [userId, createUserDto.firstName, createUserDto.lastName,
        detailsCreatedBy, detailsCreatedBy],
    );

    return userId;
  }

  async findUserByToken(client, token) {
    const res = await client.query(`SELECT user_id,created_on,validitysecs FROM password_reset_tokens  
      WHERE token = $1`, [token]);
    return Mapper.getUnique(res, UserDao.mapResponse);
  }

  async deleteToken(client, token) {
    const res = await client.query('DELETE FROM password_reset_tokens WHERE token = $1', [token]);
    return res.rowCount === 1;
  }

  async updateUser(client, updateUserDto) {
    const { sql: sql1, args: args1 } = Queries.updaterFor('users', userUpdateMap, updateUserDto);
    const res1 = await client.query(sql1, args1);

    const { sql: sql2, args: args2 } = Queries.updaterFor(
      'user_details',
      userDetailsUpdateMap,
      updateUserDto,
      'user_id',
    );
    const res2 = await client.query(sql2, args2);

    return ((res1.rowCount === 1) && (res2.rowCount === 1));
  }

  /**
   * Updates user's password
   * @param {Client} client
   * @param {string} hashedPassword
   * @returns {Promise<boolean>}
   */
  async updatePassword(client, hashedPassword, userId) {
    const { sql, args } = Queries.updaterFor('users', userPasswordUpdateMap, { hashedPassword, id: userId });
    const result = await client.query(sql, args);
    return result.rowCount === 1;
  }

  /**
   * Gets the user by emails and returns it with role mapped
   * This also have an option to only return active user
   * By default it returns all users regardless of status
   * @param {Client} client
   * @param {string} email
   * @param {boolean} [onlyActive=false]
   * @returns {Promise<RoleMappedUser>}
   */
  async findUserByEmail(client, email, onlyActive = false) {
    const preArgs = [email];
    let query = `${this.userQuery} WHERE u.email = ?`;
    if (onlyActive) {
      query += ' AND u.status = ?';
      preArgs.push(STATUS.ACTIVE);
    }
    const qb = new QueryBuilder(query, preArgs);
    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    const rolePermissions = await this.getModulesForUserOrRole(client, res.rows[0].role_id, 'role');
    const userPermissions = await this.getModulesForUserOrRole(client, res.rows[0].id, 'user');
    const user = Mapper.getUnique(res, UserDao.mapUserWithRoles);
    return { rolePermissions, userPermissions, ...user };
  }

  async getModulesForUserOrRole(client, id, type = 'role') {
    const getQuery = type === 'role'
      ? `
      SELECT DISTINCT(p.name)
      FROM permissions p
      JOIN role_permissions rp ON rp.permission_id = p.id
      WHERE rp.role_id = $1;
    `
      : `
      SELECT DISTINCT(p.name)
      FROM permissions p
      JOIN user_permissions up ON up.permission_id = p.id
      WHERE up.user_id = $1;
    `;
    const args = [id];
    const result = await client.query(getQuery, args);
    return result.rows.map((row) => row.name);
  }

  /**
   * Gets the user by id and returns it with role mapped
   * This also have an option to only return active user
   * By default it returns all users regardless of status
   * @param {Client} client
   * @param {number|string} id
   * @param {boolean} [onlyActive = false]
   * @returns {Promise<RoleMappedUser>}
   */
  async findUserById(client, id, onlyActive = false) {
    const preArgs = [id];
    let query = `${this.userQuery} WHERE u.id = ?`;
    if (onlyActive) {
      query += ' AND u.status = ?';
      preArgs.push(STATUS.ACTIVE);
    }
    const qb = new QueryBuilder(query, preArgs);
    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    const rolePermissions = await this.getModulesForUserOrRole(client, res.rows[0].role_id, 'role');
    const userPermissions = await this.getModulesForUserOrRole(client, res.rows[0].id, 'user');
    const user = Mapper.getUnique(res, UserDao.mapUserWithRoles);
    Mapper.getUnique(res, UserDao.mapUserWithRoles);
    return { rolePermissions, userPermissions, ...user };
  }

  /**
   * Update user login details and history
   * @param {Client} client
   * @param {number} userId
   * @param {import('../services/securityService').RequestDetails} requestDetails
   * @returns {Promise<boolean>}
   */
  async markUserLogin(client, userId, requestDetails) {
    const {
      ip, userAgent,
    } = requestDetails;
    const hasLoginDetails = await this.hasLoginDetails(client, userId);
    let res;
    const values = [0, null, userId];
    if (hasLoginDetails) {
      res = await client.query(`UPDATE users 
        SET  wrong_login_count = $1,
        last_wrong_login_attempt = $2 WHERE id = $3`, values);
    } else {
      res = await client.query(`INSERT INTO users  
        (wrong_login_count, last_wrong_login_attempt,id) 
        VALUES ($1,$2,$3)`, values);
    }
    await client.query(
      `INSERT INTO user_login_history
      (user_id, login_at, login_ip, login_type, user_agent, created_by) 
      VALUES ($1, $2, $3, $4, $5, $6)`,
      [userId, moment(), ip, 'NORMAL', userAgent, userId],
    );
    return res.rowCount === 1;
  }

  async markWrongLoginAttempt(client, wrongLoginCount, userId) {
    const hasLoginDetails = await this.hasLoginDetails(client, userId);
    let res;
    const values = [wrongLoginCount, moment(), userId];
    if (hasLoginDetails) {
      res = await client.query(`UPDATE users  
        SET wrong_login_count = $1, last_wrong_login_attempt = $2 
        WHERE id = $3`, values);
    } else {
      res = await client.query(`INSERT INTO users  
        (wrong_login_count, last_wrong_login_attempt, id) 
        VALUES ($1, $2, $3)`, values);
    }
    return res.rowCount === 1;
  }

  async hasLoginDetails(client, userId) {
    const res = await client.query(`SELECT id  FROM users  
      WHERE id = $1`, [userId]);
    return Mapper.getId(res) !== 0;
  }

  async deleteUserById(client, id) {
    const res = await client.query('DELETE FROM users WHERE id = $1', [id]);
    return res.rowCount === 1;
  }

  async attachRole(client, userId, role) {
    const res = await client.query(`INSERT INTO user_roles (user_id, role_id)
      VALUES ($1,(SELECT id FROM roles WHERE name = $2))`, [userId, role]);
    return res.rowCount === 1;
  }

  async findDuplicate(client, user, ignoreId) {
    const qb = new QueryBuilder(`SELECT id FROM users 
      WHERE email = ?\n`, [user.email]);

    if (ignoreId) {
      qb.append('AND id != ?', [ignoreId]);
    }

    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    return Mapper.getId(res) !== 0;
  }

  async storeUserToken(client, dto) {
    const {
      userId, token, ip, userAgent,
    } = dto;
    await client.query('DELETE FROM password_reset_tokens WHERE user_id = $1', [userId]);

    const values = [userId, token, UserDao.MAX_TOKEN_VALIDITY, ip, userAgent];
    const res = await client.query(`INSERT INTO password_reset_tokens  
    (user_id, token, validitysecs, request_ip, request_user_agent) VALUES ($1,$2,$3,$4,$5)`, values);
    return res.rowCount === 1;
  }

  async getUserPasswordResetToken(client, userId) {
    const query = `
    SELECT prt.user_id, prt.token,
    prt.created_on, prt.validity_seconds
    FROM password_reset_tokens prt
    WHERE prt.user_id = $1
    AND NOW() <= (prt.created_on + INTERVAL '1 second' * prt.validity_seconds)
    ORDER BY prt.created_on DESC
    `;
    const result = await client.query(query, [userId]);
    return Mapper.getUnique(result, UserDao.mapPasswordResetToken);
  }

  async deleteExpiredPasswordResetTokens(client) {
    const query = `
    DELETE FROM password_reset_tokens prt
    WHERE NOW() >= (prt.created_on + INTERVAL '1 second' * prt.validity_seconds)
    `;
    const res = await client.query(query);
    return res.rowCount > 0;
  }

  /**
   * @param {Client} client
   * @param {string} token
   * @param {number} userId
   * @returns {Promise.<PasswordRestToken>}
   */
  async saveToken(client, token, userId) {
    const query = `
    INSERT INTO password_reset_tokens
    (user_id, token, validity_seconds)
    VALUES
    ($1, $2, $3)
    RETURNING *
    `;
    const args = [userId, token, UserDao.MAX_TOKEN_VALIDITY];
    const res = await client.query(query, args);
    return Mapper.getUnique(res, UserDao.mapPasswordResetToken);
  }

  /**
   * Checks if the token exists (not expired)
   * @param {Client} client
   * @param {string} token
   */
  async validateResetToken(client, token) {
    const query = `
    SELECT prt.user_id, prt.token,
    prt.created_on, prt.validity_seconds
    FROM password_reset_tokens prt
    WHERE prt.token = $1
    AND NOW() <= (prt.created_on + INTERVAL '1 second' * prt.validity_seconds)
    ORDER BY prt.created_on DESC
    LIMIT 1
    `;
    const result = await client.query(query, [token]);
    if (!result.rows[0]) {
      return null;
    }
    const userId = parserInteger(result.rows[0]?.user_id);
    const user = this.findUserById(client, userId);
    return user;
  }

  async findResetPasswordTokenForUser(client, userId, token) {
    const res = await client.query(`SELECT * FROM password_reset_tokens   
    WHERE user_id = $1 AND token = $2`, [userId, token]);
    return Mapper.getUnique(res, UserDao.mapUserToken);
  }

  /**
   * Logs password reset into db
   * @param {Client} client
   * @param {import('../services/userService').resetPasswordLogDto} data
   */
  async logPasswordReset(client, data) {
    const {
      userId, actionedBy, token, actionByType, userAgent, ip, createdBy,
    } = data;
    const query = `
    INSERT INTO password_reset_history (
      user_id, actioned_by, action_by_type, token, 
      action_user_user_agent, action_user_ip,
      created_by
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7 
    );
    `;
    const args = [userId, actionedBy, actionByType, token, userAgent, ip, createdBy];
    const res = await client.query(query, args);
    return res.rowCount === 1;
  }

  static mapUserToken = (rows) => {
    const firstRow = rows[0];

    return {
      userId: parserId(firstRow.user_id),
      token: firstRow.token,
      created: parserDate(firstRow.created),
      validitySecs: parserInteger(firstRow.validitysecs),
    };
  };

  static mapResponse(rows) {
    if (rows.length === 0) return null;
    return { ...rows[0] };
  }

  /**
   * Maps user with roles
   * @param {UserFromDb[]} rows
   */
  static mapUserWithRoles = (rows) => {
    const firstRow = rows[0];
    if (!firstRow) {
      console.log('mapUserWithRoles: firstRow is %s', firstRow);
      return null;
    }
    // const role = new Role(firstRow.role);
    const { role } = firstRow;

    return {
      id: parserId(firstRow.id),
      email: firstRow.email,
      passwordHash: firstRow.password ? new PasswordHash(firstRow.password) : null,
      status: firstRow.status,
      firstName: firstRow.first_name,
      lastName: firstRow.last_name,
      name: `${firstRow.first_name} ${firstRow.last_name}`,
      wrongLoginCount: parserInteger(firstRow.wrong_login_count),
      lastWrongLoginAttempt: parserDate(firstRow.last_wrong_login_attempt),
      // lastLogin: parserDate(firstRow.last_login),
      createdOn: parserDate(firstRow.created_on),
      roleId: firstRow.role_id,
      role,
      profilePicture: firstRow.profile_picture_path,
    };
  };

  /**
   * @returns {PasswordRestToken}
   */
  static mapPasswordResetToken = (rows) => {
    const firstRow = rows[0];
    if (!firstRow) {
      console.log('mapUserWithRoles: firstRow is %s', firstRow);
      return null;
    }
    return {
      userId: parserId(firstRow.user_id),
      token: firstRow.token,
      createdOn: parserDate(firstRow.created_on),
      validitySeconds: parserInteger(firstRow.validity_seconds),
    };
  };
}

export default UserDao;

/**
 * @typedef {Object} UserFromDb
 * @property {Date} created_on
 * @property {string} email
 * @property {string} first_name
 * @property {string} id
 * @property {Date} last_login
 * @property {string} last_name
 * @property {Date} last_wrong_login_attempt
 * @property {string} password
 * @property {string} role
 * @property {string} status
 * @property {number} wrong_login_count
 */

/**
 * @typedef {Object} PasswordRestToken
 * @property {number} userId
 * @property {string} token
 * @property {moment.Moment} createdOn
 * @property {number} validitySeconds
 */

/**
 * This is the user that is fetched from the database
 * {@link UserFromDb} and mapped with the {@link Role}
 * This contains information that is parsed (type casting done)
 * This is used for internal purposes only.
 * @typedef {Object} RoleMappedUser
 * @property {number} id
 * @property {string} email
 * @property {number} passwordHash
 * @property {string} status
 * @property {string} firstName
 * @property {string} lastName
 * @property {string} name
 * @property {number} wrongLoginCount
 * @property {moment.Moment} lastWrongLoginAttempt
 * @property {moment.Moment} lastLogin
 * @property {moment.Moment} createdOn
 * @property {Role} role
 * @property {string} profilePicture
 */

/**
 * Action user is being used for all protected routes
 * It contains all the user information that are required
 * to know about the current user
 * @typedef {Object} ActionUser
 * @property {moment.Moment} createdOn
 * @property {string} email
 * @property {string} firstName
 * @property {number} id
 * @property {string} ip
 * @property {moment.Moment} lastLogin
 * @property {string} lastName
 * @property {moment.Moment} lastWrongLoginAttempt
 * @property {string} name
 * @property {string[]} rights
 * @property {Role} role
 * @property {string} status
 * @property {string} tokenAud
 * @property {string} userAgent
 * @property {number} wrongLoginCount
 */
