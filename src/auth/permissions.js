/**
 * Rights are the lowest abstraction level items in the authorization system.
 * The decision if user has access to some
 * feature or not is ultimately decided on the fact if that specific user
 * has the required. Rights are
 * never added to database in any form: they are aggregated via Roles.
 * This allows flexible tuning and
 * changing of Right items during development:
 * if you find a specific use case where current Rights
 * options are not fitting then add a new one. Just remember to
 * add that Right to appropriate Roles.
 */
class Permisssions {
  static GENERAL = Object.freeze({
    /* General Rights */
    LOGIN: 'LOGIN',
  });

  static getPermissionRights(module) {
    return this[module];
  }
}

export default Permisssions;
