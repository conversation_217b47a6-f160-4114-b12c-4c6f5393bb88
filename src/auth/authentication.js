import { isValidString, transformCase } from '../utils';
import Right from './right';

class Authentication {
  /**
   * Checks if the user has the right
   * @param {import("../modules/users/userDao").ActionUser} user
   * @param {string} right
   * @returns {boolean}
   */
  static hasRight(user, right) {
    return user.role.hasRight(right);
  }

  /**
 * Checks if user has right/module access
 * @param {Array.<String>} rights
 * @param {String} right
 * @returns {boolean}
 */
  static hasPermission(rights, right) {
    const filteredRights = rights.filter((r) => isValidString(r));
    if (filteredRights.length === 0 && rights.length === 0) {
      return false;
    }

    if (filteredRights.length === 0 && rights.length > 0) {
      console.warn('[WARN] filtered rights but rights are not empty.', 'filtered rights: = ', filteredRights, 'rights: = ', rights);
      return false;
    }
    const transformedRights = rights.map(transformCase);
    const transformedRight = transformCase(right);
    return transformedRights.indexOf(transformedRight) !== -1;
  }

  /**
   * Get the effective rights of the user
   * @param {import("../modules/users/userDao").ActionUser} user
   * @returns {string[]}
   */
  static userEffectiveRights(user) {
    const { rolePermissions, userPermissions } = user;
    const roleRights = Right.getRights(rolePermissions);
    const userRights = Right.getRights(userPermissions);
    return Array.from(new Set([...roleRights, ...userRights]));
  }
}

export default Authentication;
