import { getEnumArrayFromObj } from '../utils';
import Permissions from './permissions';

/**
 * Rights are the lowest abstraction level items in the authorization system.
 * The decision if user has access to some
 * feature or not is ultimately decided on the fact if that specific user
 * has the required. Rights are
 * never added to database in any form: they are aggregated via Roles.
 * This allows flexible tuning and
 * changing of Right items during development:
 * if you find a specific use case where current Rights
 * options are not fitting then add a new one. Just remember to
 * add that Right to appropriate Roles.
 */
class Right {
  static general = Object.freeze({
    /* General Rights */
    LOGIN: 'LOGIN',
  });

  // helper methods and stuff
  /**
   * @returns {string[]}
   */
  static allRights() {
    return [].concat(
      Right.getRightArray(this.general),
    );
  }

  /**
   * @returns {string[]}
   */

  /**
   * @returns {string[]}
   */
  static userRights() {
    return [].concat(
      Right.getRightArray(this.general),
    );
  }

  /**
   * @param {any[]} rights
   * @returns {string[]}
   */
  static getRightArray(rights) {
    return Object.freeze(Object.keys(rights).map((key) => rights[key]));
  }

  static getRights(modules) {
    const rights = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const module of modules) {
      const moduleRights = getEnumArrayFromObj(Permissions.getPermissionRights(module)) || [];
      rights.push(...moduleRights);
    }
    return rights;
  }

  /**
   * @returns {boolean}
   */
  static hasPermission(rights, val) {
    return rights.indexOf(val) !== -1;
  }

  /**
   * @param {string} val
   * @returns {boolean}
   */
  static exists(val) {
    const index = this.allRights().indexOf(val);
    return index !== -1;
  }
}

export default Right;
