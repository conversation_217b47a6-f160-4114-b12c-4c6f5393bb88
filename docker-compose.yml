version: "3.7"
services:
  api:
    build:
      context: . 
      dockerfile: Dockerfile
    container_name: unstoppable-leadership-api_api
    working_dir: /unstoppable-leadership-api
    entrypoint: ["/bin/ash", "./scripts/startup.sh"]
    volumes:
      - ./:/unstoppable-leadership-api
    depends_on:
      - db
    ports:
      - "8080:8080"
  db:
    image: postgres:14
    container_name: unstoppable-leadership-api_db
    restart: always
    ports:
      - "5427:5432"
    environment:
      POSTGRES_USER: unstoppable-leadership-api
      POSTGRES_PASSWORD: 1234567890
      POSTGRES_DB: unstoppable-leadership-api-db
    volumes:
      - ./unstoppable-leadership-api-db:/var/lib/postgresql/data