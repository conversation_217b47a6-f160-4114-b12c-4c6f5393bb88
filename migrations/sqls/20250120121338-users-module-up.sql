CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  email VA<PERSON>HAR(255) UNIQUE NOT NULL,
  first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  password VARCHAR(60), -- bcrypt hashes are always 60 bytes long
  status status NOT NULL DEFAULT 'ACTIVE',
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column	
  reserve5 VARCHAR(1), -- reserved column	
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by B<PERSON>IN<PERSON>,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGIN<PERSON>,
  deleted_on TIMESTAMPTZ,
  deleted_by BIGINT
);

CREATE TRIGGER update_users_modtime 
BEFORE UPDATE ON users 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column();

CREATE TABLE roles (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(11) UNIQUE NOT NULL,
  description TEXT,
  status status NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp
);

-- Role permissions
CREATE TABLE permissions (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(27) UNIQUE NOT NULL,
  description TEXT,
  status status NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp
);

-- User roles
CREATE TABLE user_roles (
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE RESTRICT,
  role_id BIGINT NOT NULL REFERENCES roles ON UPDATE CASCADE ON DELETE RESTRICT,
  PRIMARY KEY (user_id, role_id)
);

-- Role permissions
CREATE TABLE role_permissions (
  permission_id BIGINT NOT NULL REFERENCES permissions ON UPDATE CASCADE ON DELETE RESTRICT,
  role_id BIGINT NOT NULL REFERENCES roles ON UPDATE CASCADE ON DELETE RESTRICT,
  PRIMARY KEY (permission_id, role_id)
);

-- User permissions
CREATE TABLE user_permissions (
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE RESTRICT,
  permission_id BIGINT NOT NULL REFERENCES permissions ON UPDATE CASCADE ON DELETE RESTRICT,
  PRIMARY KEY (user_id, permission_id)
);

